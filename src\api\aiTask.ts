import config from '@/config/index'
import request from '@/utils/request/index'

const { VITE_JZT_API } = config

// 获取指定任务下章节树列表
export function getTaskBookCatalogList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/learn/getTaskBookCatalogList`, data)
}

// 查询课程概况统计数据
export function getStatistics(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/learn/getTaskStudentLearningStatistics`, data, {
    replace: true,
  })
}

// 查询课程概况表格统计数据
export function getTableStatistics(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/learn/getTaskStudentLearningList`, data, {
    replace: true,
  })
}

// 查询学生列表统计数据
export function getStudentLearnList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/learn/getTaskStudentLearnList`, data, {
    replace: true,
  })
}

// 获取答题结果列表
export function getTaskStudentExerciseList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/exercise/task/report`, data, {
    replace: true,
  })
}
// 分页查询AI任务列表
export function getAiCourseTaskList(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/getAiCourseTaskList`, data)
}
// 书籍列表
export function getBookList(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/aicourse/book/list`, data)
}
// 章节树
export function getBookCatalogTree(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/aicourse/book/catalog/tree`, data)
}
// 章节资源详情列表
export function getResourceTree(data?) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/task/aicourse/book/catalog/resource/list`, data)
}
// 查询教师下面的所有班级列表
export function getClassList(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/school/getSchoolClassList`, data)
}
// 课程编辑-章节资源详情
export function getCatalogResourceDetail(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/aicourse/book/catalog/resource/detail`, data)
}
// 修改章节资源
export function editCatalogResModify(data) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/task/aicourse/book/catalog/resource/modify`, data)
}
// 预计时长
export function computeDuration(data) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/task/aicourse/computeDuration`, data)
}
// 查询学生课程列表
export function getTaskStudentLearnList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/course/learn/getTaskStudentList`, data)
}

// 数据看板-概述统计
export function getTaskStudentLearningStatistics(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/course/learn/getTaskCourseStatisticResponseVO`, data)
}

// 数据看板-获取任务解答开关和解答上限次数
export function getConfig(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/raise/hand/getTaskStudentRaiseHandVO`, data)
}

// 数据看板-设置任务解答开关和解答上限次数
export function setTaskStudentRaiseHandApi(data) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/task/raise/hand/taskStudentRaiseHandSwitch`, data)
}

// 数据看板-一键提醒订正
export function remindCorrectApi(data) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/task/raise/hand/notifyTaskStudentMessage`, data)
}

// 数据看板-查询任务下课程列表
export function getTaskCourseList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/course/learn/getTaskStudentAiCourseList`, data)
}

// 数据看板-查询举手列表
export function getTaskStudentRaiseHandList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/raise/hand/getTaskStudentRaiseHandList`, data)
}

// 数据看板-老师解答问题
export function handleQuestion(data) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/task/raise/hand/taskStudentRaiseHandSolve`, data)
}

// 旧版老师入口提示
export function oldTeacherTip(data) {
  return request.post(
    `${VITE_JZT_API}/tutoring/admin/accountManage/warn/once`,
    data,
    { delay: false },
  )
}

// 任务模式列表
export function getModeList(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/aicourse/mode`, data)
}

// 校验时间
export function validTime(data?) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/task/aicourse/valid/Time`, data)
}
// 新建ai课程任务
export function createAiTask(data?) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/task/aicourse/newCourseTask`, data)
}

// 获取留言列表
export function getLeaveMsgList(data?) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/task/aicourse/book/catalog/resource/leaveWord/list`, data)
}

// 修改视频留言
export function editLeaveMsgModify(data?) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/task/aicourse/book/catalog/resource/leaveWord/modify`, data)
}

// 视频拆解大纲
export function getVideoAnalyse(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/book/videoAnalyse`, data)
}

// 查询指定文件夹下面的资源列表
export function getTaskResourceTeacherUploadList(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/resourceTeacherUpload/getTaskResourceTeacherUploadList`, data)
}

// ---------------- 课表  ----------------//
// 年级列表
export function getTimetableGradeList(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/school/class/timetable/grade/list`, data)
}

// 班级列表
export function getTimetableClassList(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/school/class/timetable/class/list`, data)
}

// 毕业状态
export function getTimetableIsGraduate(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/school/class/timetable/isGraduate`, data)
}

// 班级信息分页列表
export function getTimetableClassPage(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/school/class/timetable/class/page`, data)
}

// 班级课程表列表
export function getTimetableList(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/school/class/timetable/list`, data)
}

// 启用/禁用 课程表
export function changeTimetableSts(data?) {
  return request.put(`${VITE_JZT_API}/tutoring/admin/school/class/timetable/status`, data)
}

// 学期列表
export function getPhaseList(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/school/class/timetable/phase/list`, data)
}

// 课表详情
export function getTimetableDetail(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/school/class/timetable/detail`, data)
}

// 新增课程表
export function addTimetable(data?) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/school/class/timetable/add`, data)
}

// 获取上次班级考试报告
export function getLastClassExamReport(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/layer/last/class/exam/report`, data)
}

// 获取班级系统分层列表
export function getSystemLevelList(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/layer/class/system/layer`, data)
}

// 获取学生列表
export async function getStudentList(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/layer/class/student/list`,
    data,
  )
}

// 班级分数制配置回显
export function getScoreConfig(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/layer/class/score/config`, data)
}

// 保存班级分数配置
export function saveScoreConfigApi(data) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/task/layer/class/score/config`, data)
}

// 获取分层组选择列表
export async function getLayerList(data?) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/layer/layer/select`,
    data,
  )
}

// 获取考试报告详情
export function getExamReportDetail(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/layer/class/exam/report/detail`, data)
}

// 保存手动分层
export function saveManualLayer(data) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/task/layer/class/student/save`, data)
}
